# Gemini CLI OpenAI Worker Environment Variables - TESTING Environment

# Required: OAuth2 credentials J<PERSON><PERSON> from Gemini CLI authentication
# 测试环境：用于单元测试和集成测试
GCP_SERVICE_ACCOUNT={
  "access_token": "************************************************************************************************************************************************************************************************************************************",
  "refresh_token": "1//033Ra9n8Ktfo6CgYIARAAGAMSNwF-L9Ir-vzr-kdz7YoBjfvRPy8ttbRA9f01fcttkDHIxT0ei5ag6R0kwVCLq9nXJiKhWE0m24s",
  "scope": "https://www.googleapis.com/auth/cloud-platform https://www.googleapis.com/auth/userinfo.profile openid https://www.googleapis.com/auth/userinfo.email",
  "token_type": "Bearer",
  "id_token": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "expiry_date": 1753805563220
}

# Optional: Google Cloud Project ID (测试环境使用独立项目)
GEMINI_PROJECT_ID=galvanic-kit-467415-n4

# Optional: API key for authentication (测试环境API密钥)
OPENAI_API_KEY=sk-zxcvb1234567890qwertasdfg

# Optional: Enable fake thinking output for thinking models
# 测试环境禁用假思考模式
ENABLE_FAKE_THINKING=false

# Optional: Enable real Gemini thinking output
# 测试环境启用真实思考模式
ENABLE_REAL_THINKING=true

# Optional: Stream thinking as content with <thinking> tags
STREAM_THINKING_AS_CONTENT=true

# Optional: Auto switch from Pro to flash when rate-limited
ENABLE_AUTO_MODEL_SWITCHING=true

# Optional: Gemini Moderation Settings for testing
# 测试环境使用最宽松的审核设置
GEMINI_MODERATION_HARASSMENT_THRESHOLD=BLOCK_NONE
GEMINI_MODERATION_HATE_SPEECH_THRESHOLD=BLOCK_NONE
GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD=BLOCK_NONE
GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD=BLOCK_NONE
