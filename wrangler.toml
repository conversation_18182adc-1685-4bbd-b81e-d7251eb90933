# 默认配置（开发环境）
name = "gemini-cli-worker"
main = "src/index.ts"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]

# 共享KV命名空间（所有环境共用）
[[kv_namespaces]]
binding = "GEMINI_CLI_KV"
id = "30997285198946769240353074d5aaa7"

# --- 测试环境配置 ---
[env.testing]
name = "gemini-cli-worker-testing"
[[env.testing.kv_namespaces]]
binding = "GEMINI_CLI_KV"
id = "30997285198946769240353074d5aaa7"  # 共用同一个KV命名空间

# --- 预发布环境配置 ---
[env.staging]
name = "gemini-cli-worker-staging"
[[env.staging.kv_namespaces]]
binding = "GEMINI_CLI_KV"
id = "30997285198946769240353074d5aaa7"  # 共用同一个KV命名空间

# --- 预生产环境配置 ---
[env.pre-production]
name = "gemini-cli-worker-pre-prod"
[[env.pre-production.kv_namespaces]]
binding = "GEMINI_CLI_KV"
id = "30997285198946769240353074d5aaa7"  # 共用同一个KV命名空间

# --- 生产环境配置 ---
[env.production]
name = "gemini-cli-worker-prod"
[[env.production.kv_namespaces]]
binding = "GEMINI_CLI_KV"
id = "30997285198946769240353074d5aaa7"  # 共用同一个KV命名空间

# --- 本地开发配置 ---
[dev]
ip = "0.0.0.0"          # Bind to all interfaces for Docker access
port = 8787             # Port for local development server
# Note: --persist-to flag in Docker handles storage location

# --- 日志配置 ---
[observability.logs]
enabled = true
